import {
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>onte<PERSON>,
	<PERSON><PERSON>ooter,
	<PERSON>Title,
	CardDescription,
	Button,
} from '@/components/atoms';

/**
 * CardShowcase component demonstrating the new Card component
 * and Adobe Spectrum-inspired design tokens
 */
export default function CardShowcase() {
	return (
		<div className='p-8 bg-background-base min-h-screen'>
			<div className='max-w-7xl mx-auto'>
				{/* Header */}
				<div className='mb-12'>
					<h1 className='text-700 font-bold text-text-primary mb-4'>
						Card Component Showcase
					</h1>
					<p className='text-200 text-text-secondary max-w-3xl'>
						Explore our new Card component built with Adobe Spectrum-inspired
						design tokens. Each variant demonstrates different use cases and
						interactive states.
					</p>
				</div>

				{/* Card Variants */}
				<section className='mb-12'>
					<h2 className='text-500 font-semibold text-text-primary mb-6'>
						Card Variants
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						{/* Default Card */}
						<Card
							variant='default'
							size='medium'>
							<CardHeader>
								<CardTitle>Default Card</CardTitle>
								<CardDescription>
									Standard card with border and subtle shadow
								</CardDescription>
							</CardHeader>
							<CardContent>
								<p className='text-75 text-text-secondary'>
									Perfect for general content containers and information
									display.
								</p>
							</CardContent>
							<CardFooter>
								<Button
									variant='primary'
									size='sm'
									className='w-full'>
									Action
								</Button>
							</CardFooter>
						</Card>

						{/* Outlined Card */}
						<Card
							variant='outlined'
							size='medium'>
							<CardHeader>
								<CardTitle>Outlined Card</CardTitle>
								<CardDescription>
									Prominent border with no shadow
								</CardDescription>
							</CardHeader>
							<CardContent>
								<p className='text-75 text-text-secondary'>
									Great for forms, settings panels, and content that needs clear
									boundaries.
								</p>
							</CardContent>
							<CardFooter>
								<Button
									variant='outline'
									size='sm'
									className='w-full'>
									Configure
								</Button>
							</CardFooter>
						</Card>

						{/* Elevated Card */}
						<Card
							variant='elevated'
							size='medium'>
							<CardHeader>
								<CardTitle>Elevated Card</CardTitle>
								<CardDescription>
									No border with prominent shadow
								</CardDescription>
							</CardHeader>
							<CardContent>
								<p className='text-75 text-text-secondary'>
									Ideal for important content, featured items, and floating
									elements.
								</p>
							</CardContent>
							<CardFooter>
								<Button
									variant='primary'
									size='sm'
									className='w-full'>
									Featured
								</Button>
							</CardFooter>
						</Card>

						{/* Filled Card */}
						<Card
							variant='filled'
							size='medium'>
							<CardHeader>
								<CardTitle>Filled Card</CardTitle>
								<CardDescription>
									Filled background with no border or shadow
								</CardDescription>
							</CardHeader>
							<CardContent>
								<p className='text-75 text-text-secondary'>
									Perfect for subtle content grouping and background sections.
								</p>
							</CardContent>
							<CardFooter>
								<Button
									variant='ghost'
									size='sm'
									className='w-full'>
									Explore
								</Button>
							</CardFooter>
						</Card>
					</div>
				</section>

				{/* Interactive Cards */}
				<section className='mb-12'>
					<h2 className='text-500 font-semibold text-text-primary mb-6'>
						Interactive Cards
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
						<Card
							variant='default'
							size='medium'
							interactive>
							<CardHeader>
								<CardTitle>Clickable Card</CardTitle>
								<CardDescription>
									Hover and click for interactive feedback
								</CardDescription>
							</CardHeader>
							<CardContent>
								<div className='space-y-3'>
									<div className='flex items-center gap-2'>
										<div className='w-3 h-3 bg-positive-content rounded-full' />
										<span className='text-75 text-text-secondary'>
											Interactive states
										</span>
									</div>
									<div className='flex items-center gap-2'>
										<div className='w-3 h-3 bg-informative-content rounded-full' />
										<span className='text-75 text-text-secondary'>
											Hover animations
										</span>
									</div>
								</div>
							</CardContent>
						</Card>

						<Card
							variant='elevated'
							size='medium'
							interactive>
							<CardHeader>
								<CardTitle>Project Card</CardTitle>
								<CardDescription>
									Real-world example with status indicators
								</CardDescription>
							</CardHeader>
							<CardContent>
								<div className='space-y-3'>
									<div className='flex justify-between items-center'>
										<span className='text-75 text-text-secondary'>
											Progress
										</span>
										<span className='text-75 font-medium text-text-primary'>
											75%
										</span>
									</div>
									<div className='w-full bg-background-layer-1 rounded-progress h-2'>
										<div
											className='h-full bg-accent-background-default rounded-progress'
											style={{ width: '75%' }}
										/>
									</div>
									<span className='inline-flex px-2 py-1 rounded-badge text-50 font-medium text-informative-content bg-informative-background border border-informative-border'>
										In Progress
									</span>
								</div>
							</CardContent>
						</Card>

						<Card
							variant='outlined'
							size='medium'
							interactive
							disabled>
							<CardHeader>
								<CardTitle>Disabled Card</CardTitle>
								<CardDescription>Shows disabled state styling</CardDescription>
							</CardHeader>
							<CardContent>
								<p className='text-75 text-text-disabled'>
									This card demonstrates the disabled state with reduced opacity
									and no pointer events.
								</p>
							</CardContent>
						</Card>
					</div>
				</section>

				{/* Status Cards */}
				<section className='mb-12'>
					<h2 className='text-500 font-semibold text-text-primary mb-6'>
						Semantic Status Cards
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'>
						{/* Success Card */}
						<Card
							variant='default'
							size='medium'>
							<CardHeader>
								<div className='flex items-center gap-2 mb-2'>
									<div className='w-4 h-4 bg-positive-content rounded-full' />
									<CardTitle className='text-positive-content'>
										Success
									</CardTitle>
								</div>
								<CardDescription>
									Operation completed successfully
								</CardDescription>
							</CardHeader>
							<CardContent>
								<div className='p-3 bg-positive-background border border-positive-border rounded-medium'>
									<p className='text-75 text-positive-content'>
										Your changes have been saved successfully.
									</p>
								</div>
							</CardContent>
						</Card>

						{/* Error Card */}
						<Card
							variant='default'
							size='medium'>
							<CardHeader>
								<div className='flex items-center gap-2 mb-2'>
									<div className='w-4 h-4 bg-negative-content rounded-full' />
									<CardTitle className='text-negative-content'>Error</CardTitle>
								</div>
								<CardDescription>Something went wrong</CardDescription>
							</CardHeader>
							<CardContent>
								<div className='p-3 bg-negative-background border border-negative-border rounded-medium'>
									<p className='text-75 text-negative-content'>
										Please check your input and try again.
									</p>
								</div>
							</CardContent>
						</Card>

						{/* Warning Card */}
						<Card
							variant='default'
							size='medium'>
							<CardHeader>
								<div className='flex items-center gap-2 mb-2'>
									<div className='w-4 h-4 bg-notice-content rounded-full' />
									<CardTitle className='text-notice-content'>Warning</CardTitle>
								</div>
								<CardDescription>Action required</CardDescription>
							</CardHeader>
							<CardContent>
								<div className='p-3 bg-notice-background border border-notice-border rounded-medium'>
									<p className='text-75 text-notice-content'>
										This action cannot be undone.
									</p>
								</div>
							</CardContent>
						</Card>

						{/* Info Card */}
						<Card
							variant='default'
							size='medium'>
							<CardHeader>
								<div className='flex items-center gap-2 mb-2'>
									<div className='w-4 h-4 bg-informative-content rounded-full' />
									<CardTitle className='text-informative-content'>
										Info
									</CardTitle>
								</div>
								<CardDescription>Additional information</CardDescription>
							</CardHeader>
							<CardContent>
								<div className='p-3 bg-informative-background border border-informative-border rounded-medium'>
									<p className='text-75 text-informative-content'>
										New features are available in this update.
									</p>
								</div>
							</CardContent>
						</Card>
					</div>
				</section>

				{/* Size Variations */}
				<section>
					<h2 className='text-500 font-semibold text-text-primary mb-6'>
						Size Variations
					</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
						<Card
							variant='elevated'
							size='small'>
							<CardHeader>
								<CardTitle>Small Card</CardTitle>
								<CardDescription>Compact size for tight spaces</CardDescription>
							</CardHeader>
							<CardContent>
								<p className='text-75 text-text-secondary'>
									Perfect for sidebars and dense layouts.
								</p>
							</CardContent>
						</Card>

						<Card
							variant='elevated'
							size='medium'>
							<CardHeader>
								<CardTitle>Medium Card</CardTitle>
								<CardDescription>
									Standard size for most use cases
								</CardDescription>
							</CardHeader>
							<CardContent>
								<p className='text-75 text-text-secondary'>
									The default choice for most content containers.
								</p>
							</CardContent>
						</Card>

						<Card
							variant='elevated'
							size='large'>
							<CardHeader>
								<CardTitle>Large Card</CardTitle>
								<CardDescription>
									Spacious size for important content
								</CardDescription>
							</CardHeader>
							<CardContent>
								<p className='text-75 text-text-secondary'>
									Great for hero sections and featured content.
								</p>
							</CardContent>
						</Card>
					</div>
				</section>
			</div>
		</div>
	);
}
