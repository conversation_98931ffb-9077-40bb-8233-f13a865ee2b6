import type { Config } from 'tailwindcss';

const config: Config = {
	content: [
		'./src/pages/**/*.{js,ts,jsx,tsx,mdx}',
		'./src/components/**/*.{js,ts,jsx,tsx,mdx}',
		'./src/app/**/*.{js,ts,jsx,tsx,mdx}',
	],
	darkMode: 'class',
	theme: {
		extend: {
			colors: {
				// Background colors
				'background-base': 'var(--background-base)',
				'background-layer-1': 'var(--background-layer-1)',
				'background-layer-2': 'var(--background-layer-2)',
				// Surface colors
				'surface-base': 'var(--surface-base)',
				'surface-raised': 'var(--surface-raised)',
				'surface-overlay': 'var(--surface-overlay)',
				// Text colors
				'text-primary': 'var(--text-primary)',
				'text-secondary': 'var(--text-secondary)',
				'text-tertiary': 'var(--text-tertiary)',
				'text-disabled': 'var(--text-disabled)',
				'text-inverse': 'var(--text-inverse)',
				// Border colors
				'border-base': 'var(--border-base)',
				'border-strong': 'var(--border-strong)',
				'border-subtle': 'var(--border-subtle)',
				// Interactive colors
				'accent-content-default': 'var(--accent-content-default)',
				'accent-content-hover': 'var(--accent-content-hover)',
				'accent-content-down': 'var(--accent-content-down)',
				'accent-content-focus': 'var(--accent-content-focus)',
				'accent-background-default': 'var(--accent-background-default)',
				'accent-background-hover': 'var(--accent-background-hover)',
				'accent-background-down': 'var(--accent-background-down)',
				'accent-border-default': 'var(--accent-border-default)',
				'accent-border-hover': 'var(--accent-border-hover)',
				'accent-border-focus': 'var(--accent-border-focus)',
				// Semantic colors
				'positive-content': 'var(--positive-content)',
				'positive-background': 'var(--positive-background)',
				'positive-border': 'var(--positive-border)',
				'negative-content': 'var(--negative-content)',
				'negative-background': 'var(--negative-background)',
				'negative-border': 'var(--negative-border)',
				'notice-content': 'var(--notice-content)',
				'notice-background': 'var(--notice-background)',
				'notice-border': 'var(--notice-border)',
				'informative-content': 'var(--informative-content)',
				'informative-background': 'var(--informative-background)',
				'informative-border': 'var(--informative-border)',
			},
			spacing: {
				'25': 'var(--spacing-component-xs)',
				'50': 'var(--spacing-component-sm)',
				'75': 'var(--spacing-component-sm)',
				'100': 'var(--spacing-component-base)',
				'125': 'var(--spacing-component-base)',
				'150': 'var(--spacing-component-md)',
				'200': 'var(--spacing-component-lg)',
				'250': 'var(--spacing-component-lg)',
				'300': 'var(--spacing-component-xl)',
				'400': 'var(--spacing-component-2xl)',
				'500': 'var(--spacing-component-2xl)',
				'600': 'var(--spacing-component-2xl)',
				'700': 'var(--spacing-component-2xl)',
				'800': 'var(--spacing-component-2xl)',
				'900': 'var(--spacing-component-2xl)',
				'1000': 'var(--spacing-component-2xl)',
			},
			fontSize: {
				'50': ['var(--text-component-xs)', { lineHeight: '1.5' }],
				'75': ['var(--text-component-sm)', { lineHeight: '1.5' }],
				'100': ['var(--text-component-base)', { lineHeight: '1.5' }],
				'200': ['var(--text-component-lg)', { lineHeight: '1.5' }],
				'300': ['var(--text-component-xl)', { lineHeight: '1.25' }],
				'400': ['var(--text-component-2xl)', { lineHeight: '1.25' }],
				'500': ['var(--text-component-3xl)', { lineHeight: '1.25' }],
				'600': ['var(--text-component-4xl)', { lineHeight: '1.25' }],
				'700': ['var(--text-component-4xl)', { lineHeight: '1' }],
				'800': ['var(--text-component-4xl)', { lineHeight: '1' }],
				'900': ['var(--text-component-4xl)', { lineHeight: '1' }],
				// Responsive typography
				'responsive-xs': [
					'clamp(0.75rem, 0.7rem + 0.25vw, 0.875rem)',
					{ lineHeight: '1.5' },
				],
				'responsive-sm': [
					'clamp(0.875rem, 0.8rem + 0.375vw, 1rem)',
					{ lineHeight: '1.5' },
				],
				'responsive-base': [
					'clamp(1rem, 0.9rem + 0.5vw, 1.125rem)',
					{ lineHeight: '1.5' },
				],
				'responsive-lg': [
					'clamp(1.125rem, 1rem + 0.625vw, 1.25rem)',
					{ lineHeight: '1.5' },
				],
				'responsive-xl': [
					'clamp(1.25rem, 1.1rem + 0.75vw, 1.5rem)',
					{ lineHeight: '1.25' },
				],
				'responsive-2xl': [
					'clamp(1.5rem, 1.3rem + 1vw, 1.875rem)',
					{ lineHeight: '1.25' },
				],
				'responsive-3xl': [
					'clamp(1.875rem, 1.6rem + 1.375vw, 2.25rem)',
					{ lineHeight: '1.25' },
				],
				'responsive-4xl': [
					'clamp(2.25rem, 1.9rem + 1.75vw, 3rem)',
					{ lineHeight: '1' },
				],
				'responsive-5xl': [
					'clamp(3rem, 2.5rem + 2.5vw, 4rem)',
					{ lineHeight: '1' },
				],
			},
			borderRadius: {
				DEFAULT: 'var(--radius-default)',
				small: 'var(--radius-scale-small)',
				medium: 'var(--radius-scale-medium)',
				large: 'var(--radius-scale-large)',
				'extra-large': 'var(--radius-scale-xl)',
				card: 'var(--radius-card)',
				button: 'var(--radius-button)',
				input: 'var(--radius-input)',
				badge: 'var(--radius-badge)',
				progress: 'var(--radius-progress)',
			},
			boxShadow: {
				small: 'var(--shadow-component-small)',
				medium: 'var(--shadow-component-medium)',
				large: 'var(--shadow-component-large)',
			},
			height: {
				'button-small': '2rem',
				'button-medium': '2.5rem',
				'button-large': '3rem',
				'input-small': '2rem',
				'input-medium': '2.5rem',
				'input-large': '3rem',
			},
		},
	},
	plugins: [],
};

export default config;
